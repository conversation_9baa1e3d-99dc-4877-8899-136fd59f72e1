import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:audioplayers/audioplayers.dart';
import '../l10n/app_localizations.dart';
import '../services/quiz_provider.dart';
import '../services/vibration_service.dart';
import '../services/score_service.dart';

import '../widgets/simple_background.dart';
import '../widgets/main_layout.dart';
import 'results_screen.dart';

class QuizScreen extends StatefulWidget {
  const QuizScreen({super.key});

  @override
  State<QuizScreen> createState() => _QuizScreenState();
}

class _QuizScreenState extends State<QuizScreen> {
  int _timeLeft = 30;
  Timer? _timer;
  String? _selectedAnswer;
  bool _answered = false;
  late AudioPlayer _audioPlayer;
  late VibrationService _vibrationService;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();

    // Configuration spéciale pour Web
    if (kIsWeb) {
      _audioPlayer.setPlayerMode(PlayerMode.lowLatency);
      debugPrint('🌐 Mode Web détecté - Configuration audio optimisée');
    }

    _vibrationService = VibrationService();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  // Méthode pour jouer le son de bonne réponse
  Future<void> _playCorrectSound() async {
    // Essaie plusieurs formats pour compatibilité Web
    final soundFiles = [
      'sounds/correct.wav',
      'sounds/correct.mp3',
      'sounds/button.mp3', // Fallback
    ];

    for (String soundFile in soundFiles) {
      try {
        debugPrint('🎵 Tentative: $soundFile');
        await _audioPlayer.play(AssetSource(soundFile));
        debugPrint('✅ Son correct joué: $soundFile');
        return; // Succès, on sort
      } catch (e) {
        debugPrint('❌ Échec $soundFile: $e');
        continue; // Essaie le suivant
      }
    }
    debugPrint('❌ Aucun son correct disponible');
  }

  // Méthode pour jouer le son de mauvaise réponse
  Future<void> _playIncorrectSound() async {
    // Essaie plusieurs formats pour compatibilité Web
    final soundFiles = [
      'sounds/echec.wav',
      'sounds/echec.mp3',
      'sounds/incorrect.mp3', // Fallback
    ];

    for (String soundFile in soundFiles) {
      try {
        debugPrint('🎵 Tentative: $soundFile');
        await _audioPlayer.play(AssetSource(soundFile));
        debugPrint('✅ Son incorrect joué: $soundFile');
        return; // Succès, on sort
      } catch (e) {
        debugPrint('❌ Échec $soundFile: $e');
        continue; // Essaie le suivant
      }
    }
    debugPrint('❌ Aucun son incorrect disponible');
  }

  void _startTimer() {
    _timeLeft = 30;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_timeLeft > 0) {
          _timeLeft--;
        } else {
          _timer?.cancel();
          _handleTimeout();
        }
      });
    });
  }

  void _handleTimeout() {
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);

    // If not answered, mark as incorrect
    if (!_answered) {
      _playIncorrectSound();
      _vibrationService.vibrateOnAnswer(false);
      quizProvider.answerQuestion('');
      _moveToNextQuestion();
    }
  }

  void _moveToNextQuestion() {
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);

    // Reset state for next question
    setState(() {
      _selectedAnswer = null;
      _answered = false;
    });

    // Delay before moving to next question
    Future.delayed(const Duration(seconds: 1), () {
      if (quizProvider.currentQuestionIndex <
          quizProvider.questions.length - 1) {
        quizProvider.nextQuestion();
        _startTimer();
        // Pas de son pour les transitions entre questions
      } else {
        // Save high score and navigate to results screen
        quizProvider.saveHighScore(quizProvider.score);
        // Pas de son de fin de quiz

        // Also save to score service for leaderboard
        final scoreService = ScoreService();
        scoreService.addScore(
          ScoreEntry(
            category: quizProvider.questions[0].category,
            difficulty: quizProvider.questions[0].difficulty,
            score: quizProvider.score,
            totalQuestions: quizProvider.questions.length,
            date: DateTime.now(),
          ),
        );

        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const ResultsScreen()),
          );
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final quizProvider = Provider.of<QuizProvider>(context);
    final l10n = AppLocalizations.of(context);
    final currentQuestion =
        quizProvider.questions[quizProvider.currentQuestionIndex];

    return MainLayout(
      title:
          '${l10n.question} ${quizProvider.currentQuestionIndex + 1}/${quizProvider.questions.length}',
      currentIndex: -1, // Pas d'onglet sélectionné pour cette page
      showBottomNav: false, // Masquer la navigation en bas pour cette page
      child: SimpleBackground(
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Timer et score avec design simple
                SimpleCard(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Timer
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color:
                                  _timeLeft < 10
                                      ? Colors.red.withValues(alpha: 0.2)
                                      : Theme.of(context).colorScheme.primary
                                          .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.timer,
                              color:
                                  _timeLeft < 10
                                      ? Colors.red
                                      : Theme.of(context).colorScheme.primary,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            '$_timeLeft ${l10n.seconds}',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color:
                                  _timeLeft < 10
                                      ? Colors.red
                                      : Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                      // Score
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Theme.of(
                                context,
                              ).colorScheme.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.star,
                              color: Theme.of(context).colorScheme.primary,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${l10n.score}: ${quizProvider.score}',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Barre de progression simple
                SimpleCard(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Progression',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${quizProvider.currentQuestionIndex + 1}/${quizProvider.questions.length}',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      SimpleProgressIndicator(
                        value:
                            (quizProvider.currentQuestionIndex + 1) /
                            quizProvider.questions.length,
                        color: Theme.of(context).colorScheme.primary,
                        backgroundColor: Colors.grey.withValues(alpha: 0.3),
                        height: 8,
                        borderRadius: 4,
                      ),
                    ],
                  ),
                ),

                // Catégorie et difficulté avec design simple
                SimpleCard(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 20),
                  child: Row(
                    children: [
                      Expanded(
                        child: SimpleChip(
                          label: currentQuestion.displayCategory,
                          icon: Icons.category,
                          backgroundColor: Colors.grey.withValues(alpha: 0.1),
                          textColor: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(width: 12),
                      SimpleChip(
                        label: currentQuestion.difficulty.toUpperCase(),
                        icon: _getDifficultyIcon(currentQuestion.difficulty),
                        backgroundColor: _getDifficultyColor(
                          currentQuestion.difficulty,
                        ),
                        textColor: Colors.white,
                      ),
                    ],
                  ),
                ),

                // Question avec design simple
                SimpleCard(
                  padding: const EdgeInsets.all(20),
                  margin: const EdgeInsets.only(bottom: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Theme.of(
                                context,
                              ).colorScheme.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.quiz,
                              color: Theme.of(context).colorScheme.primary,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Question',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        currentQuestion.displayQuestion,
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),

                // Answer options
                const SizedBox(height: 24),
                Expanded(
                  child: ListView.builder(
                    itemCount: currentQuestion.displayAllAnswers.length,
                    itemBuilder: (context, index) {
                      final answer = currentQuestion.displayAllAnswers[index];

                      // Determine button color and icon based on selection and correctness
                      Color? buttonColor;
                      IconData? buttonIcon;

                      if (_answered) {
                        if (answer == currentQuestion.displayCorrectAnswer) {
                          // Bonne réponse - VERT avec icône de succès
                          buttonColor = Colors.green.shade600;
                          buttonIcon = Icons.check_circle;
                        } else if (answer == _selectedAnswer) {
                          // Mauvaise réponse sélectionnée - ROUGE avec icône d'erreur
                          buttonColor = Colors.red.shade600;
                          buttonIcon = Icons.cancel;
                        } else {
                          // Autres réponses - gris
                          buttonColor = Colors.grey.shade400;
                        }
                      } else if (answer == _selectedAnswer) {
                        // Réponse en cours de sélection - bleu
                        buttonColor = Colors.blue.shade600;
                      }

                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12.0),
                        child: ElevatedButton(
                          onPressed:
                              _answered
                                  ? null
                                  : () {
                                    final isCorrect =
                                        answer ==
                                        currentQuestion.displayCorrectAnswer;

                                    // Play sound and vibrate based on correctness
                                    debugPrint(
                                      '🎮 QUIZ: Réponse cliquée: $answer',
                                    );
                                    debugPrint(
                                      '🎮 QUIZ: Réponse correcte: ${currentQuestion.displayCorrectAnswer}',
                                    );
                                    debugPrint(
                                      '🎮 QUIZ: Est correct: $isCorrect',
                                    );

                                    if (isCorrect) {
                                      debugPrint('🎯 Bonne réponse !');
                                      _playCorrectSound();
                                      _vibrationService.vibrateOnAnswer(true);
                                    } else {
                                      debugPrint('❌ Mauvaise réponse !');
                                      _playIncorrectSound();
                                      _vibrationService.vibrateOnAnswer(false);
                                    }

                                    setState(() {
                                      _selectedAnswer = answer;
                                      _answered = true;
                                    });
                                    _timer?.cancel();
                                    quizProvider.answerQuestion(answer);
                                    _moveToNextQuestion();
                                  },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: buttonColor,
                            foregroundColor:
                                buttonColor != null ? Colors.white : null,
                            padding: const EdgeInsets.symmetric(
                              vertical: 16,
                              horizontal: 20,
                            ),
                            alignment: Alignment.centerLeft,
                            elevation: buttonColor != null ? 4 : 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side:
                                  buttonColor != null
                                      ? BorderSide(color: buttonColor, width: 2)
                                      : BorderSide.none,
                            ),
                          ),
                          child: Row(
                            children: [
                              // Icône à gauche (si disponible)
                              if (buttonIcon != null) ...[
                                Icon(buttonIcon, color: Colors.white, size: 20),
                                const SizedBox(width: 12),
                              ],
                              // Texte de la réponse
                              Expanded(
                                child: Text(
                                  answer,
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return Colors.green.withValues(alpha: 0.8);
      case 'medium':
        return Colors.orange.withValues(alpha: 0.8);
      case 'hard':
        return Colors.red.withValues(alpha: 0.8);
      default:
        return Colors.grey.withValues(alpha: 0.8);
    }
  }

  IconData _getDifficultyIcon(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return Icons.sentiment_satisfied;
      case 'medium':
        return Icons.sentiment_neutral;
      case 'hard':
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.help_outline;
    }
  }
}
