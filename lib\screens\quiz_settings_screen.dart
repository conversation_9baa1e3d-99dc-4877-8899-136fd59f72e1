import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/quiz_provider.dart';
import '../services/localization_service.dart';
import '../widgets/rtl_aware_widget.dart';

import '../widgets/simple_background.dart';
import '../widgets/main_layout.dart';
import 'quiz_screen.dart';

class QuizSettingsScreen extends StatefulWidget {
  const QuizSettingsScreen({super.key});

  @override
  State<QuizSettingsScreen> createState() => _QuizSettingsScreenState();
}

class _QuizSettingsScreenState extends State<QuizSettingsScreen> {
  int? _selectedCategoryId;
  String _selectedDifficulty = 'medium';
  int _selectedQuestionCount = 10;

  @override
  Widget build(BuildContext context) {
    final quizProvider = Provider.of<QuizProvider>(context);
    final l10n = AppLocalizations.of(context);

    return MainLayout(
      title: l10n.quizSettings,
      currentIndex: -1, // Pas d'onglet sélectionné pour cette page
      showBottomNav: false, // Masquer la navigation en bas pour cette page
      child: Consumer<LocalizationService>(
        builder: (context, localizationService, _) {
          return SimpleBackground(
            child:
                quizProvider.isLoading
                    ? Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    )
                    : SafeArea(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            const SizedBox(height: 20),

                            // Section Catégorie avec design simple
                            SimpleCard(
                              margin: const EdgeInsets.only(bottom: 24),
                              padding: const EdgeInsets.all(24),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.category,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          size: 20,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      RTLAwareText(
                                        l10n.selectCategory,
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.onSurface,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),
                                  _buildCategoryDropdown(
                                    quizProvider,
                                    l10n,
                                    localizationService,
                                  ),
                                ],
                              ),
                            ),

                            // Section Difficulté avec design simple
                            SimpleCard(
                              margin: const EdgeInsets.only(bottom: 24),
                              padding: const EdgeInsets.all(24),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.tune,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          size: 20,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      RTLAwareText(
                                        l10n.selectDifficulty,
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.onSurface,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),
                                  _buildDifficultySelector(
                                    l10n,
                                    localizationService,
                                  ),
                                ],
                              ),
                            ),

                            // Section Nombre de questions avec design simple
                            SimpleCard(
                              margin: const EdgeInsets.only(bottom: 32),
                              padding: const EdgeInsets.all(24),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.quiz,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          size: 20,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      RTLAwareText(
                                        l10n.numberOfQuestions,
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.onSurface,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),
                                  _buildQuestionCountSelector(
                                    quizProvider,
                                    localizationService,
                                  ),
                                ],
                              ),
                            ),

                            // Bouton de démarrage simple
                            Center(
                              child: SimpleButton(
                                text: l10n.startQuiz,
                                icon: Icons.play_arrow,
                                onPressed:
                                    _selectedCategoryId == null
                                        ? null
                                        : () => _startQuiz(context),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 48,
                                  vertical: 16,
                                ),
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),

                            const SizedBox(height: 40),
                          ],
                        ),
                      ),
                    ),
          );
        },
      ),
    );
  }

  Widget _buildCategoryDropdown(
    QuizProvider quizProvider,
    AppLocalizations l10n,
    LocalizationService localizationService,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(16),
      ),
      child: DropdownButton<int>(
        value: _selectedCategoryId,
        isExpanded: true,
        hint: RTLAwareText(
          l10n.selectCategory,
          style: TextStyle(color: Colors.grey[600]),
        ),
        underline: const SizedBox(),
        dropdownColor: Theme.of(context).colorScheme.surface,
        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
        icon: Icon(
          Icons.arrow_drop_down,
          color: Theme.of(context).colorScheme.onSurface,
        ),
        items:
            quizProvider.translatedCategories.map((category) {
              return DropdownMenuItem<int>(
                value: category['id'],
                child: RTLAwareText(
                  category['name'],
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              );
            }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedCategoryId = value;
          });
        },
      ),
    );
  }

  Widget _buildDifficultySelector(
    AppLocalizations l10n,
    LocalizationService localizationService,
  ) {
    return Consumer<QuizProvider>(
      builder: (context, quizProvider, child) {
        return Row(
          children:
              quizProvider.translatedDifficulties.map((difficulty) {
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: _buildDifficultyOption(
                      difficulty['name']!,
                      difficulty['value']!,
                      localizationService,
                    ),
                  ),
                );
              }).toList(),
        );
      },
    );
  }

  Widget _buildDifficultyOption(
    String label,
    String value,
    LocalizationService localizationService,
  ) {
    final isSelected = _selectedDifficulty == value;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedDifficulty = value;
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color:
                  isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey.withValues(alpha: 0.3),
              width: 2,
            ),
            boxShadow:
                isSelected
                    ? [
                      BoxShadow(
                        color: Theme.of(
                          context,
                        ).colorScheme.primary.withValues(alpha: 0.3),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ]
                    : null,
          ),
          child: RTLAwareText(
            label,
            style: TextStyle(
              color:
                  isSelected
                      ? Colors.white
                      : Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildQuestionCountSelector(
    QuizProvider quizProvider,
    LocalizationService localizationService,
  ) {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      alignment: WrapAlignment.center,
      children:
          quizProvider.questionCounts.map((count) {
            final isSelected = _selectedQuestionCount == count;
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedQuestionCount = count;
                  });
                },
                borderRadius: BorderRadius.circular(50),
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color:
                          isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey.withValues(alpha: 0.3),
                      width: 3,
                    ),
                    boxShadow:
                        isSelected
                            ? [
                              BoxShadow(
                                color: Theme.of(
                                  context,
                                ).colorScheme.primary.withValues(alpha: 0.4),
                                blurRadius: 12,
                                spreadRadius: 3,
                              ),
                            ]
                            : null,
                  ),
                  child: Center(
                    child: RTLAwareText(
                      count.toString(),
                      style: TextStyle(
                        color:
                            isSelected
                                ? Colors.white
                                : Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }

  void _startQuiz(BuildContext context) async {
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);
    final l10n = AppLocalizations.of(context);

    // Show loading dialog with RTL support
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Consumer<LocalizationService>(
            builder: (context, localizationService, _) {
              return Directionality(
                textDirection: localizationService.textDirection,
                child: AlertDialog(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 20),
                      RTLAwareText(
                        l10n.loading,
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
    );

    try {
      // Fetch questions
      await quizProvider.fetchQuestions(
        categoryId: _selectedCategoryId!,
        difficulty: _selectedDifficulty,
        amount: _selectedQuestionCount,
      );

      // Close loading dialog
      if (context.mounted) {
        Navigator.pop(context);
      }

      // Navigate to quiz screen if questions were loaded successfully
      if (context.mounted && quizProvider.questions.isNotEmpty) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const QuizScreen()),
        );
      } else if (context.mounted) {
        // Show error message if no questions were loaded
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: RTLAwareText(l10n.loadingError),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog on error
      if (context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: RTLAwareText(l10n.loadingError),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }
}
