import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/theme_provider.dart';
import '../services/localization_service.dart';
import '../services/simple_auth_service.dart';
import '../widgets/rtl_aware_widget.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final authService = Provider.of<SimpleAuthService>(context);
    final l10n = AppLocalizations.of(context);

    return ListView(
      children: [
        // User info section
        if (authService.isAuthenticated) ...[
          _buildSectionHeader(l10n.userAccount),
          _buildUserInfo(authService),
          const Divider(),
        ],

        // Language section - Essentiel pour l'application de quiz
        _buildSectionHeader(l10n.language),
        _buildLanguageSelector(l10n, context),

        const Divider(),

        // Theme section - Optionnel mais utile
        _buildSectionHeader(l10n.theme),
        _buildThemeSelector(themeProvider, l10n),

        // Logout section
        if (authService.isAuthenticated) ...[
          const Divider(),
          _buildSectionHeader(l10n.account),
          _buildLogoutButton(authService, l10n),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildThemeSelector(
    ThemeProvider themeProvider,
    AppLocalizations l10n,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(Icons.light_mode),
              label: Text(l10n.lightTheme),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    !themeProvider.isDarkMode
                        ? Theme.of(context).colorScheme.primary
                        : null,
                foregroundColor:
                    !themeProvider.isDarkMode ? Colors.white : null,
              ),
              onPressed:
                  themeProvider.isDarkMode
                      ? () => themeProvider.toggleTheme()
                      : null,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(Icons.dark_mode),
              label: Text(l10n.darkTheme),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    themeProvider.isDarkMode
                        ? Theme.of(context).colorScheme.primary
                        : null,
                foregroundColor: themeProvider.isDarkMode ? Colors.white : null,
              ),
              onPressed:
                  !themeProvider.isDarkMode
                      ? () => themeProvider.toggleTheme()
                      : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector(AppLocalizations l10n, BuildContext context) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, child) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 8,
                children:
                    localizationService.supportedLocales.map((locale) {
                      final isSelected =
                          localizationService.locale.languageCode ==
                          locale.languageCode;
                      return ChoiceChip(
                        label: Text(
                          localizationService.getLanguageName(
                            locale.languageCode,
                          ),
                        ),
                        selected: isSelected,
                        onSelected: (selected) async {
                          if (selected) {
                            await localizationService.setLocale(locale);
                            // Forcer la mise à jour de l'interface
                            if (mounted) {
                              setState(() {});
                            }
                          }
                        },
                      );
                    }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUserInfo(SimpleAuthService authService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: RTLAwareText(
                      authService.currentUserName
                              ?.substring(0, 1)
                              .toUpperCase() ??
                          'U',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RTLAwareText(
                          authService.currentUserName ?? 'Utilisateur',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        RTLAwareText(
                          authService.currentUserEmail ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutButton(
    SimpleAuthService authService,
    AppLocalizations l10n,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ElevatedButton.icon(
        icon: const Icon(Icons.logout),
        label: RTLAwareText(l10n.logout),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          minimumSize: const Size(double.infinity, 48),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onPressed: () async {
          final shouldLogout = await showDialog<bool>(
            context: context,
            builder:
                (context) => AlertDialog(
                  title: RTLAwareText(l10n.logoutConfirmTitle),
                  content: RTLAwareText(l10n.logoutConfirmMessage),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context, false),
                      child: RTLAwareText(l10n.cancel),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context, true),
                      child: RTLAwareText(l10n.logout),
                    ),
                  ],
                ),
          );

          if (shouldLogout == true) {
            try {
              await authService.signOut();
            } catch (e) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: RTLAwareText(l10n.logoutError),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          }
        },
      ),
    );
  }
}
