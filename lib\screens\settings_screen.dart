import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/theme_provider.dart';
import '../services/localization_service.dart';
import '../services/simple_auth_service.dart';
import '../widgets/rtl_aware_widget.dart';
import '../screens/welcome_screen.dart';
import '../services/notification_service.dart';
import '../services/audio_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final authService = Provider.of<SimpleAuthService>(context);
    final l10n = AppLocalizations.of(context);

    return ListView(
      children: [
        // User info section
        if (authService.isAuthenticated) ...[
          _buildSectionHeader(l10n.userAccount),
          _buildUserInfo(authService),
          const Divider(),
        ],

        // Language section - Essentiel pour l'application de quiz
        _buildSectionHeader(l10n.language),
        _buildLanguageSelector(l10n, context),

        const Divider(),

        // Theme section - Optionnel mais utile
        _buildSectionHeader(l10n.theme),
        _buildThemeSelector(themeProvider, l10n),

        const Divider(),

        // Audio test section
        _buildSectionHeader('Test Audio'),
        _buildAudioTestSection(l10n),

        const Divider(),

        // Notifications test section
        _buildSectionHeader('Test Notifications'),
        _buildNotificationTestSection(l10n),

        // Logout section
        if (authService.isAuthenticated) ...[
          const Divider(),
          _buildSectionHeader(l10n.account),
          _buildLogoutButton(authService, l10n),
        ],

        // Navigation section
        const Divider(),
        _buildSectionHeader(l10n.navigation),
        _buildNavigationSection(l10n),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildThemeSelector(
    ThemeProvider themeProvider,
    AppLocalizations l10n,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(Icons.light_mode),
              label: Text(l10n.lightTheme),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    !themeProvider.isDarkMode
                        ? Theme.of(context).colorScheme.primary
                        : null,
                foregroundColor:
                    !themeProvider.isDarkMode ? Colors.white : null,
              ),
              onPressed:
                  themeProvider.isDarkMode
                      ? () => themeProvider.toggleTheme()
                      : null,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(Icons.dark_mode),
              label: Text(l10n.darkTheme),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    themeProvider.isDarkMode
                        ? Theme.of(context).colorScheme.primary
                        : null,
                foregroundColor: themeProvider.isDarkMode ? Colors.white : null,
              ),
              onPressed:
                  !themeProvider.isDarkMode
                      ? () => themeProvider.toggleTheme()
                      : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector(AppLocalizations l10n, BuildContext context) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, child) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 8,
                children:
                    localizationService.supportedLocales.map((locale) {
                      final isSelected =
                          localizationService.locale.languageCode ==
                          locale.languageCode;
                      return ChoiceChip(
                        label: Text(
                          localizationService.getLanguageName(
                            locale.languageCode,
                          ),
                        ),
                        selected: isSelected,
                        onSelected: (selected) async {
                          if (selected) {
                            await localizationService.setLocale(locale);
                            // Forcer la mise à jour de l'interface
                            if (mounted) {
                              setState(() {});
                            }
                          }
                        },
                      );
                    }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUserInfo(SimpleAuthService authService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: RTLAwareText(
                      authService.currentUserName
                              ?.substring(0, 1)
                              .toUpperCase() ??
                          'U',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RTLAwareText(
                          authService.currentUserName ?? 'Utilisateur',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        RTLAwareText(
                          authService.currentUserEmail ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutButton(
    SimpleAuthService authService,
    AppLocalizations l10n,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ElevatedButton.icon(
        icon: const Icon(Icons.logout),
        label: RTLAwareText(l10n.logout),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          minimumSize: const Size(double.infinity, 48),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onPressed: () async {
          final shouldLogout = await showDialog<bool>(
            context: context,
            builder:
                (context) => AlertDialog(
                  title: RTLAwareText(l10n.logoutConfirmTitle),
                  content: RTLAwareText(l10n.logoutConfirmMessage),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context, false),
                      child: RTLAwareText(l10n.cancel),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context, true),
                      child: RTLAwareText(l10n.logout),
                    ),
                  ],
                ),
          );

          if (shouldLogout == true) {
            try {
              await authService.signOut();
            } catch (e) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: RTLAwareText(l10n.logoutError),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          }
        },
      ),
    );
  }

  Widget _buildNavigationSection(AppLocalizations l10n) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          ElevatedButton.icon(
            icon: const Icon(Icons.home),
            label: RTLAwareText(l10n.home),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 48),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: () {
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(builder: (context) => const WelcomeScreen()),
                (route) => false,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAudioTestSection(AppLocalizations l10n) {
    final audioService = AudioService();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Audio test buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.check_circle, color: Colors.green),
                  label: const Text('Son Correct'),
                  onPressed: () => audioService.playCorrectSound(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.withValues(alpha: 0.1),
                    foregroundColor: Colors.green,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.cancel, color: Colors.red),
                  label: const Text('Son Échec'),
                  onPressed: () => audioService.playIncorrectSound(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.withValues(alpha: 0.1),
                    foregroundColor: Colors.red,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.touch_app, color: Colors.blue),
                  label: const Text('Son Bouton'),
                  onPressed: () => audioService.playButtonSound(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.withValues(alpha: 0.1),
                    foregroundColor: Colors.blue,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.celebration, color: Colors.orange),
                  label: const Text('Son Succès'),
                  onPressed: () => audioService.playSuccessSound(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange.withValues(alpha: 0.1),
                    foregroundColor: Colors.orange,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Audio settings toggle
          SwitchListTile(
            title: const Text('Sons activés'),
            subtitle: const Text('Activer/désactiver les effets sonores'),
            value: audioService.isSoundEnabled,
            onChanged: (bool value) async {
              await audioService.setSoundEnabled(value);
              if (mounted) {
                setState(() {});

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(value ? 'Sons activés' : 'Sons désactivés'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationTestSection(AppLocalizations l10n) {
    final notificationService = NotificationService();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Test notification buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.notifications),
                  label: const Text('Test Simple'),
                  onPressed: () => _testSimpleNotification(l10n),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.notification_important),
                  label: const Text('Test Important'),
                  onPressed: () => _testImportantNotification(l10n),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.quiz),
                  label: const Text('Test Quiz'),
                  onPressed: () => _testQuizNotification(l10n),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.celebration),
                  label: const Text('Test Succès'),
                  onPressed: () => _testSuccessNotification(l10n),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Notification settings toggle
          SwitchListTile(
            title: const Text('Notifications activées'),
            subtitle: const Text('Activer/désactiver les notifications'),
            value: notificationService.areNotificationsEnabled,
            onChanged: (bool value) async {
              await notificationService.setNotificationsEnabled(value);
              if (mounted) {
                setState(() {});

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      value
                          ? 'Notifications activées'
                          : 'Notifications désactivées',
                    ),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  void _testSimpleNotification(AppLocalizations l10n) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: RTLAwareText('📱 Notification simple de test'),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _testImportantNotification(AppLocalizations l10n) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            icon: const Icon(
              Icons.notification_important,
              color: Colors.orange,
            ),
            title: RTLAwareText('🚨 Notification Importante'),
            content: RTLAwareText(
              'Ceci est une notification importante de test. '
              'Elle apparaît sous forme de dialogue pour attirer l\'attention.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: RTLAwareText('OK'),
              ),
            ],
          ),
    );
  }

  void _testQuizNotification(AppLocalizations l10n) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.quiz, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: RTLAwareText(
                '🎯 Nouveau quiz disponible ! Testez vos connaissances.',
              ),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Voir',
          textColor: Colors.white,
          onPressed: () {
            // Navigation vers quiz
          },
        ),
      ),
    );
  }

  void _testSuccessNotification(AppLocalizations l10n) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            icon: const Icon(Icons.celebration, color: Colors.green),
            title: RTLAwareText('🎉 Félicitations !'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RTLAwareText(
                  'Vous avez terminé un quiz avec un excellent score !',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      RTLAwareText(
                        '🏆 Score: 95%',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      RTLAwareText(
                        'Catégorie: Sciences',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: RTLAwareText('Fermer'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Navigation vers nouveau quiz
                },
                child: RTLAwareText('Nouveau Quiz'),
              ),
            ],
          ),
    );
  }
}
