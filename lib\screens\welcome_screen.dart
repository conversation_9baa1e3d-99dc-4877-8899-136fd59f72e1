import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:audioplayers/audioplayers.dart';
import '../l10n/app_localizations.dart';
import '../services/quiz_provider.dart';
import '../services/vibration_service.dart';
import '../services/localization_service.dart';
import '../widgets/rtl_aware_widget.dart';
import '../widgets/simple_background.dart';

import 'quiz_settings_screen.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<QuizProvider>(context, listen: false).initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    final audioPlayer = AudioPlayer();
    final vibrationService = VibrationService();
    final l10n = AppLocalizations.of(context);

    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return SimpleBackground(
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child:
                localizationService.isChangingLanguage
                    ? Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    )
                    : SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 40),

                            // Logo animé avec effet néon
                            TweenAnimationBuilder<double>(
                              duration: const Duration(milliseconds: 1200),
                              tween: Tween(begin: 0.0, end: 1.0),
                              curve: Curves.elasticOut,
                              builder: (context, value, child) {
                                return Transform.scale(
                                  scale: value,
                                  child: Container(
                                    width: 120,
                                    height: 120,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: LinearGradient(
                                        colors: [
                                          Theme.of(context).colorScheme.primary,
                                          Theme.of(
                                            context,
                                          ).colorScheme.secondary,
                                          Theme.of(
                                            context,
                                          ).colorScheme.tertiary,
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withValues(alpha: 0.4),
                                          blurRadius: 30,
                                          spreadRadius: 8,
                                        ),
                                        BoxShadow(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .secondary
                                              .withValues(alpha: 0.3),
                                          blurRadius: 20,
                                          spreadRadius: 4,
                                        ),
                                      ],
                                    ),
                                    child: const Icon(
                                      Icons.quiz,
                                      size: 60,
                                      color: Colors.white,
                                    ),
                                  ),
                                );
                              },
                            ),

                            const SizedBox(height: 40),

                            // Titre principal
                            SimpleCard(
                              padding: const EdgeInsets.all(28),
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              child: Column(
                                children: [
                                  RTLAwareText(
                                    l10n.welcomeMessage,
                                    style: TextStyle(
                                      fontSize: 28,
                                      fontWeight: FontWeight.bold,
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 16),
                                  RTLAwareText(
                                    l10n.welcomeDescription,
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withValues(alpha: 0.7),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 40),

                            // Bouton principal
                            SimpleButton(
                              text: l10n.startQuiz,
                              icon: Icons.play_arrow,
                              onPressed: () {
                                vibrationService.vibrateOnButtonPress();
                                try {
                                  audioPlayer.play(
                                    AssetSource('sounds/button.mp3'),
                                  );
                                  debugPrint('🔊 Button sound played');
                                } catch (e) {
                                  debugPrint(
                                    '❌ Error playing button sound: $e',
                                  );
                                }
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => const QuizSettingsScreen(),
                                  ),
                                );
                              },
                              padding: const EdgeInsets.symmetric(
                                horizontal: 48,
                                vertical: 16,
                              ),
                              color: Theme.of(context).colorScheme.primary,
                            ),

                            const SizedBox(height: 20),

                            // Bouton À propos
                            SimpleButton(
                              text: l10n.about,
                              icon: Icons.info_outline,
                              onPressed: () {
                                vibrationService.vibrateOnButtonPress();
                                try {
                                  audioPlayer.play(
                                    AssetSource('sounds/button.mp3'),
                                  );
                                  debugPrint('🔊 About button sound played');
                                } catch (e) {
                                  debugPrint(
                                    '❌ Error playing about button sound: $e',
                                  );
                                }
                                _showAboutDialog(context);
                              },
                              padding: const EdgeInsets.symmetric(
                                horizontal: 32,
                                vertical: 12,
                              ),
                              color: Theme.of(context).colorScheme.secondary,
                              isSecondary: true,
                            ),

                            const SizedBox(height: 20),

                            // Bouton de test audio (temporaire pour diagnostic)
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                ElevatedButton.icon(
                                  icon: const Icon(
                                    Icons.volume_up,
                                    color: Colors.green,
                                  ),
                                  label: const Text('Test Correct'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green.withValues(
                                      alpha: 0.1,
                                    ),
                                    foregroundColor: Colors.green,
                                  ),
                                  onPressed: () async {
                                    try {
                                      debugPrint('🧪 Testing correct sound...');
                                      await audioPlayer.play(
                                        AssetSource('sounds/correct.mp3'),
                                      );
                                      debugPrint(
                                        '✅ Correct sound test completed',
                                      );
                                    } catch (e) {
                                      debugPrint(
                                        '❌ Correct sound test failed: $e',
                                      );
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              'Erreur son correct: $e',
                                            ),
                                          ),
                                        );
                                      }
                                    }
                                  },
                                ),
                                ElevatedButton.icon(
                                  icon: const Icon(
                                    Icons.volume_up,
                                    color: Colors.red,
                                  ),
                                  label: const Text('Test Incorrect'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red.withValues(
                                      alpha: 0.1,
                                    ),
                                    foregroundColor: Colors.red,
                                  ),
                                  onPressed: () async {
                                    try {
                                      debugPrint(
                                        '🧪 Testing incorrect sound...',
                                      );
                                      await audioPlayer.play(
                                        AssetSource('sounds/incorrect.mp3'),
                                      );
                                      debugPrint(
                                        '✅ Incorrect sound test completed',
                                      );
                                    } catch (e) {
                                      debugPrint(
                                        '❌ Incorrect sound test failed: $e',
                                      );
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              'Erreur son incorrect: $e',
                                            ),
                                          ),
                                        );
                                      }
                                    }
                                  },
                                ),
                              ],
                            ),

                            const SizedBox(height: 40),
                          ],
                        ),
                      ),
                    ),
          ),
        );
      },
    );
  }

  void _showAboutDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Consumer<LocalizationService>(
          builder: (context, localizationService, _) {
            return Directionality(
              textDirection: localizationService.textDirection,
              child: AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: RTLAwareText(
                  l10n.aboutTitle,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SimpleCard(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RTLAwareText(
                              l10n.aboutDescription,
                              style: TextStyle(
                                fontSize: 16,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 16),
                            RTLAwareText(
                              l10n.aboutApi,
                              style: TextStyle(
                                fontSize: 14,
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      RTLAwareText(
                        l10n.aboutFeatures,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildFeatureItem(l10n.aboutFeature1, Icons.category),
                      _buildFeatureItem(l10n.aboutFeature2, Icons.tune),
                      _buildFeatureItem(l10n.aboutFeature3, Icons.score),
                      _buildFeatureItem(l10n.aboutFeature4, Icons.feedback),
                    ],
                  ),
                ),
                actions: [
                  SimpleButton(
                    text: l10n.close,
                    onPressed: () => Navigator.of(context).pop(),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 8,
                    ),
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildFeatureItem(String text, IconData icon) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              if (!localizationService.isRTL) ...[
                Icon(
                  icon,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
              ],
              Expanded(
                child: RTLAwareText(
                  text,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              if (localizationService.isRTL) ...[
                const SizedBox(width: 8),
                Icon(
                  icon,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
