import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();

  // Joue le son de bonne réponse
  Future<void> playCorrectSound() async {
    debugPrint('🔊 DEBUT playCorrectSound()');
    try {
      debugPrint('🎵 Tentative de lecture: sounds/correct.mp3');
      await _audioPlayer.play(AssetSource('sounds/correct.mp3'));
      debugPrint('✅ Son correct joué avec succès');
    } catch (e) {
      debugPrint('❌ ERREUR son correct: $e');
      debugPrint('❌ Type erreur: ${e.runtimeType}');
    }
  }

  // Joue le son de mauvaise réponse
  Future<void> playIncorrectSound() async {
    debugPrint('🔊 DEBUT playIncorrectSound()');
    try {
      debugPrint('🎵 Tentative de lecture: sounds/echec.mp3');
      await _audioPlayer.play(AssetSource('sounds/echec.mp3'));
      debugPrint('✅ Son échec joué avec succès');
    } catch (e) {
      debugPrint('❌ ERREUR echec.mp3: $e');
      // Si echec.mp3 n'existe pas, essaie incorrect.mp3
      try {
        debugPrint('🎵 Fallback vers: sounds/incorrect.mp3');
        await _audioPlayer.play(AssetSource('sounds/incorrect.mp3'));
        debugPrint('✅ Son incorrect joué avec succès');
      } catch (e2) {
        debugPrint('❌ ERREUR FINALE son incorrect: $e2');
        debugPrint('❌ Type erreur: ${e2.runtimeType}');
      }
    }
  }

  void dispose() {
    _audioPlayer.dispose();
  }
}
