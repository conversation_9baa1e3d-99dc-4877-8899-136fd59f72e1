import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isSoundEnabled = true;

  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _isSoundEnabled = prefs.getBool('sound_enabled') ?? true;
  }

  Future<void> playCorrectSound() async {
    debugPrint('🔊 playCorrectSound called - Sound enabled: $_isSoundEnabled');
    if (!_isSoundEnabled) {
      debugPrint('🔇 Sound disabled, not playing correct sound');
      return;
    }
    try {
      debugPrint('🎵 Playing correct sound: sounds/correct.mp3');
      await _audioPlayer.play(AssetSource('sounds/correct.mp3'));
      debugPrint('✅ Correct sound played successfully');
    } catch (e) {
      debugPrint('❌ Error playing correct sound: $e');
    }
  }

  Future<void> playIncorrectSound() async {
    debugPrint(
      '🔊 playIncorrectSound called - Sound enabled: $_isSoundEnabled',
    );
    if (!_isSoundEnabled) {
      debugPrint('🔇 Sound disabled, not playing incorrect sound');
      return;
    }
    try {
      debugPrint('🎵 Playing incorrect sound: sounds/echec.mp3');
      await _audioPlayer.play(AssetSource('sounds/echec.mp3'));
      debugPrint('✅ Incorrect sound played successfully');
    } catch (e) {
      debugPrint('❌ Error playing incorrect sound: $e');
    }
  }

  Future<void> playButtonSound() async {
    if (!_isSoundEnabled) return;
    try {
      await _audioPlayer.play(AssetSource('sounds/button.mp3'));
      debugPrint('Playing button sound');
    } catch (e) {
      debugPrint('Error playing button sound: $e');
    }
  }

  Future<void> playTimerSound() async {
    if (!_isSoundEnabled) return;
    try {
      await _audioPlayer.play(AssetSource('sounds/timer.mp3'));
      debugPrint('Playing timer sound');
    } catch (e) {
      debugPrint('Error playing timer sound: $e');
    }
  }

  Future<void> playSuccessSound() async {
    if (!_isSoundEnabled) return;
    try {
      await _audioPlayer.play(AssetSource('sounds/correct.mp3'));
      debugPrint('Playing success sound');
    } catch (e) {
      debugPrint('Error playing success sound: $e');
    }
  }

  Future<void> setSoundEnabled(bool enabled) async {
    _isSoundEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('sound_enabled', enabled);
  }

  bool get isSoundEnabled => _isSoundEnabled;
}
