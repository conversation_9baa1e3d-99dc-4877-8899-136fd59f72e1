import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();

  // Joue le son de bonne réponse
  Future<void> playCorrectSound() async {
    try {
      await _audioPlayer.play(AssetSource('sounds/correct.mp3'));
      debugPrint('✅ Son correct joué');
    } catch (e) {
      debugPrint('❌ Erreur son correct: $e');
    }
  }

  // Joue le son de mauvaise réponse
  Future<void> playIncorrectSound() async {
    try {
      // Essaie d'abord echec.mp3
      await _audioPlayer.play(AssetSource('sounds/echec.mp3'));
      debugPrint('✅ Son échec joué');
    } catch (e) {
      // Si echec.mp3 n'existe pas, essaie incorrect.mp3
      try {
        await _audioPlayer.play(AssetSource('sounds/echec.mp3'));
        debugPrint('✅ Son incorrect joué');
      } catch (e2) {
        debugPrint('❌ Erreur son incorrect: $e2');
      }
    }
  }

  void dispose() {
    _audioPlayer.dispose();
  }
}
