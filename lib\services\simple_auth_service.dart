import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Version simplifiée d'AuthService sans Firebase pour les tests
class SimpleAuthService extends ChangeNotifier {
  bool _isAuthenticated = false;
  String? _currentUserEmail;
  String? _currentUserName;

  bool get isAuthenticated => _isAuthenticated;
  String? get currentUserEmail => _currentUserEmail;
  String? get currentUserName => _currentUserName;

  // Simulation de connexion
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    // Simulation d'un délai réseau
    await Future.delayed(const Duration(seconds: 1));

    // Validation simple
    if (email.isEmpty || password.isEmpty) {
      throw 'Email et mot de passe requis';
    }

    if (!email.contains('@')) {
      throw 'Email invalide';
    }

    if (password.length < 6) {
      throw 'Mot de passe trop court';
    }

    // Simulation de connexion réussie
    _isAuthenticated = true;
    _currentUserEmail = email;
    _currentUserName =
        email.split('@')[0]; // Utilise la partie avant @ comme nom

    // Sauvegarder dans SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isAuthenticated', true);
    await prefs.setString('userEmail', email);
    await prefs.setString('userName', _currentUserName!);

    notifyListeners();
  }

  // Déconnexion
  Future<void> signOut() async {
    _isAuthenticated = false;
    _currentUserEmail = null;
    _currentUserName = null;

    // Supprimer de SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('isAuthenticated');
    await prefs.remove('userEmail');
    await prefs.remove('userName');

    notifyListeners();
  }

  // Réinitialisation du mot de passe (simulation)
  Future<void> resetPassword(String email) async {
    await Future.delayed(const Duration(seconds: 1));

    if (email.isEmpty || !email.contains('@')) {
      throw 'Email invalide';
    }

    // Simulation d'envoi d'email réussi
    // Dans une vraie app, ceci enverrait un email
  }

  // Charger l'état depuis SharedPreferences
  Future<void> loadAuthState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isAuthenticated = prefs.getBool('isAuthenticated') ?? false;
      _currentUserEmail = prefs.getString('userEmail');
      _currentUserName = prefs.getString('userName');
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors du chargement de l\'état d\'authentification: $e');
      }
    }
  }

  // Mettre à jour les statistiques utilisateur (simulation)
  Future<void> updateUserStats({
    required int score,
    required int totalQuestions,
    required String category,
    required String difficulty,
  }) async {
    // Dans une vraie app avec Firebase, ceci sauvegarderait dans Firestore
    // Pour l'instant, on ne fait rien
    if (kDebugMode) {
      print(
        'Stats mises à jour: $score/$totalQuestions pour $category ($difficulty)',
      );
    }
  }

  // Obtenir les données utilisateur (simulation)
  Future<Map<String, dynamic>?> getUserData() async {
    if (!_isAuthenticated) return null;

    return {
      'email': _currentUserEmail,
      'displayName': _currentUserName,
      'totalQuizzes': 0,
      'bestScore': 0,
      'averageScore': 0.0,
    };
  }
}
